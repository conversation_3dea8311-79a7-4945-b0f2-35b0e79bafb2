import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/gestures.dart';

class ExamInfoScreen extends StatelessWidget {
  const ExamInfoScreen({Key? key}) : super(key: key);

  // Helper method to launch URLs
  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }

  // Helper method to create clickable text spans
  TextSpan _buildTextWithLinks(String text, BuildContext context) {
    // Define link regex pattern
    final urlPattern = RegExp(r'https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)');
    
    // Find all links in text
    final matches = urlPattern.allMatches(text);
    
    // If no links found, return simple text
    if (matches.isEmpty) {
      return TextSpan(
        text: text, 
        style: const TextStyle(
          fontSize: 16, 
          height: 1.5,
          color: Colors.black87, // Added dark color for better visibility
        ),
      );
    }

    // Build rich text with clickable links
    final List<TextSpan> spans = [];
    int lastMatchEnd = 0;

    for (final match in matches) {
      // Add text before link
      if (match.start > lastMatchEnd) {
        spans.add(TextSpan(
          text: text.substring(lastMatchEnd, match.start),
          style: const TextStyle(fontSize: 16, height: 1.5, color: Colors.black87),
        ));
      }

      // Add clickable link
      final url = text.substring(match.start, match.end);
      spans.add(TextSpan(
        text: url,
        style: const TextStyle(
          fontSize: 16,
          height: 1.5,
          color: Colors.blue,
          decoration: TextDecoration.underline,
        ),
        recognizer: TapGestureRecognizer()
          ..onTap = () => _launchUrl(url),
      ));

      lastMatchEnd = match.end;
    }

    // Add remaining text after last link
    if (lastMatchEnd < text.length) {
      spans.add(TextSpan(
        text: text.substring(lastMatchEnd),
        style: const TextStyle(fontSize: 16, height: 1.5, color: Colors.black87),
      ));
    }

    return TextSpan(children: spans);
  }

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> examInfo = [
      {
        'title': '🗓️ مواعيد مهمة',
        'icon': Icons.calendar_today_rounded,
        'content': '• فترة التقديم الإلكتروني:\n   من ١٥ أيار حتى ١٠ حزيران ٢٠٢٥\n\n'
            '• موعد الامتحان التنافسي:\n   الاثنين ٣٠ حزيران ٢٠٢٥\n\n'
            '• المقابلات الشخصية:\n   من ٣٠ حزيران حتى ١ تموز ٢٠٢٥\n\n'
            '• إعلان نتائج الامتحان:\n   ٢ تموز ٢٠٢٥\n\n'
            '• إعلان نتائج القبول الأولية:\n   ١٠ تموز ٢٠٢٥\n\n'
            '• فترة الاعتراضات:\n   من ١٣ إلى ١٥ تموز ٢٠٢٥\n\n'
            '• إعلان نتائج الاعتراضات:\n   ٢٠ تموز ٢٠٢٥\n\n'
            '• إعلان نتائج القبول النهائية:\n   ٢٤ تموز ٢٠٢٥\n\n'
            '• إصدار الأوامر الإدارية:\n   ١٢ آب ٢٠٢٥\n\n'
            '• مباشرة الطلبة المقبولين:\n   ١ أيلول ٢٠٢٥'
      },
      {
        'title': '✅ شروط التقديم',
        'icon': Icons.assignment_outlined,
        'content': '• العمر:\n   ألا يزيد عن ٤٥ عامًا لبرامج الماجستير والدبلوم العالي.\n\n'
            '• المعدل:\n   ألا يقل معدل البكالوريوس عن ٦٥٪ للمتقدمين على الماجستير.\n\n'
            '• الامتحان التنافسي:\n   اجتياز الامتحان التنافسي المحدد.\n\n'
            '• المقابلة الشخصية:\n   حضور المقابلة التي تعقدها الكليات بعد ظهور نتائج الامتحان التنافسي.\n\n'
            '• السجل التأديبي:\n   ألا يكون المتقدم مشمولًا بإجراءات الفصل أو العقوبات التأديبية من جهة دراسته السابقة.\n\n'
            '• الوثائق:\n   توفير مستمسكات أصلية ومصدقة تشمل الشهادات، والوثائق الثبوتية، والهوية الوطنية.'
      },
      {
        'title': '📄 خطوات التقديم الإلكتروني',
        'icon': Icons.laptop_chromebook_rounded,
        'content': '١. زيارة الموقع الرسمي للتقديم:\n   https://adm.rdd.edu.iq\n\n'
            '٢. قراءة جميع التعليمات والضوابط المنشورة على الموقع.\n\n'
            '٣. الدخول إلى صفحة الاستمارة الإلكترونية.\n\n'
            '٤. ملء جميع الحقول المطلوبة بدقة:\n'
            '   • الاسم الكامل\n'
            '   • رقم الهوية\n'
            '   • المؤهل العلمي\n'
            '   • اسم الجامعة\n'
            '   • سنة التخرج\n\n'
            '٥. رفع المستندات المطلوبة بصيغة PDF:\n'
            '   • شهادة التخرج\n'
            '   • كشف الدرجات\n'
            '   • هوية الأحوال المدنية\n'
            '   • تأييد الوظيفة (إن وجد)\n\n'
            '٦. تحديد الرغبة في التقديم إلى جامعة أو أكثر حسب التخصصات المتاحة.\n\n'
            '٧. إرسال الطلب إلكترونيًا والاحتفاظ بنسخة من الاستمارة بعد تأكيد الاستلام.'
      },
      {
        'title': '📚 مناهج الامتحان التنافسي',
        'icon': Icons.book_outlined,
        'content': 'يمكنك طلب مناهج الامتحان التنافسي للدراسات العليا في الجامعة العراقية عبر الخدمة الإلكترونية المتاحة على بوابة أور:\n\n'
            '🔗 https://eservice.ur.gov.iq/index/new-service/10487'
      },
      {
        'title': 'ℹ️ ملاحظات إضافية',
        'icon': Icons.info_outline_rounded,
        'content': '• يُنصح بالاطلاع على ضوابط التقديم والقبول في الدراسات العليا للعام الدراسي ٢٠٢٥–٢٠٢٦، والتي يمكن العثور عليها على موقع وزارة التعليم العالي والبحث العلمي:\n\n'
            '🔗 https://mohesr.gov.iq/ar/post/ضوابط-التقديم-والقبول-في-الدراسات-العليا-للعام-الدراسي-20262025-2025-03-04-18\n\n'
            '• تأكد من جاهزية جميع الوثائق قبل البدء في عملية التقديم لتفادي استبعاد الطلب.'
      },
    ];

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        title: const Text('معلومات الامتحان التنافسي',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 22,
            letterSpacing: 0.5,
          ),
        ),
        backgroundColor: Colors.transparent,
        flexibleSpace: ClipRRect(
          borderRadius: const BorderRadius.vertical(
            bottom: Radius.circular(25),
          ),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.teal.shade800.withOpacity(0.9), 
                    Colors.teal.shade500.withOpacity(0.8)
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(25),
                ),
              ),
            ),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.teal.shade50,
              Colors.white,
              Colors.white,
              Colors.teal.shade50,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Card(
                  elevation: 4,
                  shadowColor: const Color(0xFF009688).withOpacity(0.4),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.white, Colors.teal.shade50],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [Colors.teal.shade300, Colors.teal.shade600],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.teal.shade200.withOpacity(0.6),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.school_rounded,
                            size: 36,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'معلومات الامتحان التنافسي',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.teal.shade800,
                            letterSpacing: 0.2,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.teal.shade100),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.teal.shade50.withOpacity(0.3),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Text(
                            'اضغط على العناوين للحصول على معلومات مفصلة',
                            style: TextStyle(
                              fontSize: 15,
                              color: Colors.grey.shade800,
                              height: 1.3,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    physics: const BouncingScrollPhysics(),
                    itemCount: examInfo.length,
                    itemBuilder: (context, index) {
                      return AnimatedContainer(
                        duration: Duration(milliseconds: 300 + (index * 50)),
                        curve: Curves.easeInOut,
                        transform: Matrix4.translationValues(0, 0, 0)
                          ..scale(1.0),
                        margin: const EdgeInsets.only(bottom: 14),
                        child: Card(
                          elevation: 2,
                          shadowColor: Colors.teal.withOpacity(0.3),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [Colors.white, Colors.teal.shade50],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: Theme(
                                data: Theme.of(context).copyWith(
                                  dividerColor: Colors.transparent,
                                  colorScheme: Theme.of(context).colorScheme.copyWith(
                                    background: Colors.transparent,
                                  ),
                                ),
                                child: ExpansionTile(
                                  childrenPadding: EdgeInsets.zero,
                                  tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                  leading: Container(
                                    width: 46,
                                    height: 46,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [Colors.teal.shade300, Colors.teal.shade600],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.teal.shade200.withOpacity(0.4),
                                          blurRadius: 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Icon(
                                      examInfo[index]['icon'],
                                      color: Colors.white,
                                      size: 24,
                                    ),
                                  ),
                                  title: Text(
                                    examInfo[index]['title'],
                                    style: TextStyle(
                                      fontSize: 17,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.teal.shade800,
                                    ),
                                  ),
                                  children: [
                                    Container(
                                      width: double.infinity,
                                      padding: const EdgeInsets.all(18),
                                      decoration: BoxDecoration(
                                        color: Colors.teal.shade50,
                                        borderRadius: const BorderRadius.only(
                                          bottomLeft: Radius.circular(12),
                                          bottomRight: Radius.circular(12),
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.teal.shade100.withOpacity(0.3),
                                            blurRadius: 2,
                                            offset: const Offset(0, -1),
                                          ),
                                        ],
                                      ),
                                      // Replace the Text widget with RichText for clickable links
                                      child: RichText(
                                        textAlign: TextAlign.right,
                                        text: _buildTextWithLinks(examInfo[index]['content'], context),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
