import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:ui';

class UniversityLinksScreen extends StatelessWidget {
  const UniversityLinksScreen({Key? key}) : super(key: key);

  Future<void> _launchUrl(String urlString, BuildContext context) async {
    final Uri url = Uri.parse(urlString);
    try {
      if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
        throw Exception('Could not launch $url');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('لا يمكن فتح الرابط: $urlString'),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.redAccent.shade700,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          elevation: 6,
          margin: const EdgeInsets.all(10),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> universities = [
      {'name': 'جميع الجامعات', 'url': 'http://adm.rdd.edu.iq/'},
      {'name': 'جامعة بغداد', 'url': 'http://hs.uob.edu.iq/'},
      {'name': 'الجامعة المستنصرية', 'url': 'https://postgrad.uomustansiriyah.edu.iq/'},
      {'name': 'جامعة الانبار', 'url': 'https://uoa-post.com/'},
      {'name': 'جامعة الموصل', 'url': 'https://pgadmission.uomosul.edu.iq/'},
      {'name': 'جامعة بابل', 'url': 'https://postgradapp.uobabylon.edu.iq/'},
      {'name': 'الجامعة التكنولوجية', 'url': 'https://gradapply.uotechnology.edu.iq/'},
      {'name': 'جامعة سامراء', 'url': 'https://uosamarra.rdd.edu.iq/'},
      {'name': 'الجامعة التقنية الوسطى', 'url': 'https://postgraduate.mtu.edu.iq/'},
      {'name': 'جامعة النهرين', 'url': 'https://hadm.nahrainuniv.edu.iq/'},
      {'name': 'الجامعة التقنية الشمالية', 'url': 'https://postgraduate.ntuapp.net/'},
      {'name': 'جامعة البصرة', 'url': 'https://uobasrah.rdd.edu.iq/'},
      {'name': 'جامعة الكوفة', 'url': 'https://uokufa.rdd.edu.iq/'},
      {'name': 'الجامعة العراقية', 'url': 'https://aliraqia.rdd.edu.iq/'},
      {'name': 'جامعة ديالى', 'url': 'https://uodiyala.rdd.edu.iq/'},
      {'name': 'جامعة تكريت', 'url': 'https://tu.rdd.edu.iq/'},
      {'name': 'جامعة الفلوجة', 'url': 'https://uofallujah.rdd.edu.iq/'},
      {'name': 'كلية الإمام الكاظم عليه السلام', 'url': 'https://iku.rdd.edu.iq/'},
      {'name': 'كلية الإمام الأعظم', 'url': 'https://imamaladham.rdd.edu.iq/'},
      {'name': 'جامعة ذي قار', 'url': 'https://utq.rdd.edu.iq/'},
      {'name': 'جامعة المثنى', 'url': 'https://mu.rdd.edu.iq/'},
    ];

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        title: const Text(
          'روابط التقديم للجامعات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 22,
            letterSpacing: 0.5,
          ),
        ),
        backgroundColor: Colors.transparent,
        flexibleSpace: ClipRRect(
          borderRadius: const BorderRadius.vertical(
            bottom: Radius.circular(25),
          ),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.indigo.shade900.withOpacity(0.9),
                    Colors.indigo.shade500.withOpacity(0.8)
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(25),
                ),
              ),
            ),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.indigo.shade50,
              Colors.white,
              Colors.white,
              Colors.indigo.shade50,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(12.0, 10.0, 12.0, 12.0),
            child: Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 600),
                child: Column(
                  children: [
                    Card(
                      elevation: 4,
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Container(
                        padding: const EdgeInsets.all(16.0),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Colors.indigo.shade100, Colors.white],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Column(
                          children: [
                            CircleAvatar(
                              backgroundColor: Colors.indigo.shade100,
                              radius: 28,
                              child: const Icon(
                                Icons.school,
                                color: Colors.indigo,
                                size: 28,
                              ),
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              'روابط التقديم للجامعات العراقية',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.indigo,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 12),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.indigo.shade100, width: 1),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.indigo.shade100.withOpacity(0.5),
                                    blurRadius: 2,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                              child: const Text(
                                'اضغط على اسم الجامعة للانتقال إلى صفحة التقديم',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w500,
                                  height: 1.2,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Expanded(
                      child: ListView.builder(
                        physics: const BouncingScrollPhysics(),
                        itemCount: universities.length,
                        itemBuilder: (context, index) {
                          final university = universities[index];
                          return AnimatedContainer(
                            duration: Duration(milliseconds: 300 + (index * 30)),
                            curve: Curves.easeInOut,
                            margin: const EdgeInsets.only(bottom: 8),
                            child: Card(
                              margin: EdgeInsets.zero,
                              elevation: 2,
                              shadowColor: Colors.indigo.withOpacity(0.2),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.white,
                                      index % 2 == 0
                                          ? Colors.indigo.shade50
                                          : Colors.amber.shade50,
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                clipBehavior: Clip.antiAlias,
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(12),
                                    splashColor: Colors.indigo.withOpacity(0.1),
                                    onTap: () => _launchUrl(university['url'], context),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                                      child: Row(
                                        children: [
                                          Container(
                                            width: 40,
                                            height: 40,
                                            decoration: BoxDecoration(
                                              gradient: LinearGradient(
                                                colors: index % 2 == 0
                                                    ? [Colors.indigo.shade400, Colors.indigo.shade700]
                                                    : [Colors.amber.shade400, Colors.amber.shade700],
                                                begin: Alignment.topLeft,
                                                end: Alignment.bottomRight,
                                              ),
                                              shape: BoxShape.circle,
                                              boxShadow: [
                                                BoxShadow(
                                                  color: index % 2 == 0
                                                      ? Colors.indigo.withOpacity(0.2)
                                                      : Colors.amber.withOpacity(0.2),
                                                  blurRadius: 3,
                                                  offset: const Offset(0, 2),
                                                ),
                                              ],
                                            ),
                                            child: Center(
                                              child: Text(
                                                '${index + 1}',
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            child: Text(
                                              university['name'],
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                color: index % 2 == 0
                                                    ? Colors.indigo.shade700
                                                    : Colors.amber.shade800,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Container(
                                            padding: const EdgeInsets.all(6),
                                            decoration: BoxDecoration(
                                              color: index % 2 == 0
                                                  ? Colors.indigo.withOpacity(0.1)
                                                  : Colors.amber.withOpacity(0.1),
                                              shape: BoxShape.circle,
                                            ),
                                            child: Icon(
                                              Icons.open_in_new,
                                              size: 18,
                                              color: index % 2 == 0
                                                  ? Colors.indigo
                                                  : Colors.amber.shade800,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
