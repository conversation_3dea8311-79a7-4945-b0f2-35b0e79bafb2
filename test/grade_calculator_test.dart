import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tanafusi/screens/grade_calculator_screen.dart';

void main() {
  group('Grade Calculator Tests', () {
    test('Calculate relative grade correctly', () {
      // Test data
      double applicantGrade = 85.0;
      double firstStudentGrade = 95.0;

      // Expected calculation:
      // المعدل النسبي = (معدل الطالب المتقدم × 100 / معدل الطالب الأول + معدل الطالب المتقدم / 2) / 2
      // = (85 * 100 / 95 + 85 / 2) / 2
      // = (89.47 + 42.5) / 2
      // = 131.97 / 2
      // = 65.985

      double expectedRelativeGrade = ((applicantGrade * 100 / firstStudentGrade) + (applicantGrade / 2)) / 2;
      double actualRelativeGrade = ((applicantGrade * 100 / firstStudentGrade) + (applicantGrade / 2)) / 2;

      expect(actualRelativeGrade, closeTo(expectedRelativeGrade, 0.01));
      expect(actualRelativeGrade, closeTo(65.985, 0.01));
    });

    test('Calculate competition base grade correctly', () {
      // Test data
      double relativeGrade = 65.985;
      double competitiveExam = 80.0;

      // Expected calculation:
      // معدل أساس المفاضلة = 70% من المعدل النسبي + 30% من الامتحان التنافسي
      // = 65.985 * 0.7 + 80 * 0.3
      // = 46.1895 + 24
      // = 70.1895

      double expectedCompetitionBaseGrade = (relativeGrade * 0.7) + (competitiveExam * 0.3);
      double actualCompetitionBaseGrade = (relativeGrade * 0.7) + (competitiveExam * 0.3);

      expect(actualCompetitionBaseGrade, closeTo(expectedCompetitionBaseGrade, 0.01));
      expect(actualCompetitionBaseGrade, closeTo(70.1895, 0.01));
    });

    test('Full calculation test with different values', () {
      // Test case 1
      double applicantGrade1 = 90.0;
      double firstStudentGrade1 = 98.0;
      double competitiveExam1 = 75.0;

      double relativeGrade1 = ((applicantGrade1 * 100 / firstStudentGrade1) + (applicantGrade1 / 2)) / 2;
      double competitionBaseGrade1 = (relativeGrade1 * 0.7) + (competitiveExam1 * 0.3);

      // Expected: ((90*100/98) + (90/2))/2 = (91.84 + 45)/2 = 68.42
      // Competition: 68.42*0.7 + 75*0.3 = 47.894 + 22.5 = 70.394
      expect(relativeGrade1, closeTo(68.42, 0.01));
      expect(competitionBaseGrade1, closeTo(70.394, 0.01));

      // Test case 2
      double applicantGrade2 = 78.0;
      double firstStudentGrade2 = 92.0;
      double competitiveExam2 = 85.0;

      double relativeGrade2 = ((applicantGrade2 * 100 / firstStudentGrade2) + (applicantGrade2 / 2)) / 2;
      double competitionBaseGrade2 = (relativeGrade2 * 0.7) + (competitiveExam2 * 0.3);

      // Expected: ((78*100/92) + (78/2))/2 = (84.78 + 39)/2 = 61.89
      // Competition: 61.89*0.7 + 85*0.3 = 43.323 + 25.5 = 68.823
      expect(relativeGrade2, closeTo(61.89, 0.01));
      expect(competitionBaseGrade2, closeTo(68.823, 0.01));
    });

    test('Edge cases', () {
      // Test when applicant grade equals first student grade
      double applicantGrade = 95.0;
      double firstStudentGrade = 95.0;
      double competitiveExam = 90.0;

      double relativeGrade = ((applicantGrade * 100 / firstStudentGrade) + (applicantGrade / 2)) / 2;
      double competitionBaseGrade = (relativeGrade * 0.7) + (competitiveExam * 0.3);

      // Expected: ((95*100/95) + (95/2))/2 = (100 + 47.5)/2 = 73.75
      // Competition: 73.75*0.7 + 90*0.3 = 51.625 + 27 = 78.625
      expect(relativeGrade, closeTo(73.75, 0.01));
      expect(competitionBaseGrade, closeTo(78.625, 0.01));
    });

    test('Minimum values', () {
      // Test with minimum realistic values
      double applicantGrade = 50.0;
      double firstStudentGrade = 100.0;
      double competitiveExam = 50.0;

      double relativeGrade = ((applicantGrade * 100 / firstStudentGrade) + (applicantGrade / 2)) / 2;
      double competitionBaseGrade = (relativeGrade * 0.7) + (competitiveExam * 0.3);

      // Expected: ((50*100/100) + (50/2))/2 = (50 + 25)/2 = 37.5
      // Competition: 37.5*0.7 + 50*0.3 = 26.25 + 15 = 41.25
      expect(relativeGrade, closeTo(37.5, 0.01));
      expect(competitionBaseGrade, closeTo(41.25, 0.01));
    });
  });

  group('PhD Grade Calculator Tests', () {
    test('Calculate PhD competition grade correctly', () {
      // Test data
      double mastersGrade = 85.0;
      double competitiveExam = 80.0;

      // Expected calculation:
      // معدل المفاضلة = معدل الماجستير × 0.6 + درجة التنافسي × 0.4
      // = 85 * 0.6 + 80 * 0.4
      // = 51 + 32
      // = 83

      double expectedCompetitionGrade = (mastersGrade * 0.6) + (competitiveExam * 0.4);
      double actualCompetitionGrade = (mastersGrade * 0.6) + (competitiveExam * 0.4);

      expect(actualCompetitionGrade, closeTo(expectedCompetitionGrade, 0.01));
      expect(actualCompetitionGrade, closeTo(83.0, 0.01));
    });

    test('PhD calculation with different values', () {
      // Test case 1
      double mastersGrade1 = 90.0;
      double competitiveExam1 = 75.0;

      double competitionGrade1 = (mastersGrade1 * 0.6) + (competitiveExam1 * 0.4);

      // Expected: 90*0.6 + 75*0.4 = 54 + 30 = 84
      expect(competitionGrade1, closeTo(84.0, 0.01));

      // Test case 2
      double mastersGrade2 = 78.0;
      double competitiveExam2 = 85.0;

      double competitionGrade2 = (mastersGrade2 * 0.6) + (competitiveExam2 * 0.4);

      // Expected: 78*0.6 + 85*0.4 = 46.8 + 34 = 80.8
      expect(competitionGrade2, closeTo(80.8, 0.01));
    });

    test('PhD edge cases', () {
      // Test with equal values
      double mastersGrade = 80.0;
      double competitiveExam = 80.0;

      double competitionGrade = (mastersGrade * 0.6) + (competitiveExam * 0.4);

      // Expected: 80*0.6 + 80*0.4 = 48 + 32 = 80
      expect(competitionGrade, closeTo(80.0, 0.01));
    });

    test('PhD minimum realistic values', () {
      // Test with minimum realistic values
      double mastersGrade = 65.0;
      double competitiveExam = 50.0;

      double competitionGrade = (mastersGrade * 0.6) + (competitiveExam * 0.4);

      // Expected: 65*0.6 + 50*0.4 = 39 + 20 = 59
      expect(competitionGrade, closeTo(59.0, 0.01));
    });
  });

  group('Grade Calculator Widget Tests', () {
    testWidgets('Masters calculator screen loads correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const GradeCalculatorScreen(studyType: StudyType.masters),
          locale: const Locale('ar', 'SA'),
        ),
      );

      // التحقق من وجود العناصر الأساسية للماجستير
      expect(find.text('حساب المعدل النسبي والتفاضلي - ماجستير'), findsOneWidget);
      expect(find.text('معدل الطالب المتقدم'), findsOneWidget);
      expect(find.text('معدل الطالب الأول'), findsOneWidget);
      expect(find.text('درجة الامتحان التنافسي'), findsOneWidget);
      expect(find.text('احسب المعدل'), findsOneWidget);
      expect(find.text('إعادة تعيين'), findsOneWidget);
    });

    testWidgets('PhD calculator screen loads correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const GradeCalculatorScreen(studyType: StudyType.phd),
          locale: const Locale('ar', 'SA'),
        ),
      );

      // التحقق من وجود العناصر الأساسية للدكتوراه
      expect(find.text('حساب معدل المفاضلة - دكتوراه'), findsOneWidget);
      expect(find.text('معدل شهادة الماجستير'), findsOneWidget);
      expect(find.text('درجة الامتحان التنافسي'), findsOneWidget);
      expect(find.text('احسب المعدل'), findsOneWidget);
      expect(find.text('إعادة تعيين'), findsOneWidget);
      // التأكد من عدم وجود حقل معدل الطالب الأول
      expect(find.text('معدل الطالب الأول'), findsNothing);
    });
  });
}
