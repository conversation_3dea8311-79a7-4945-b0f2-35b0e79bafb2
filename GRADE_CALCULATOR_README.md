# حاسبة المعدل النسبي والتفاضلي للدراسات العليا

## نظرة عامة
تم إضافة ميزة شاملة لحساب المعدلات للطلاب المتقدمين للدراسات العليا في الجامعات العراقية، مع دعم كامل لكل من **الماجستير** و **الدكتوراه** بمعادلات مختلفة لكل نوع.

## المعادلات المستخدمة

### 🎓 للماجستير

#### 1. المعدل النسبي
```
المعدل النسبي = (معدل الطالب المتقدم × 100 ÷ معدل الطالب الأول + معدل الطالب المتقدم ÷ 2) ÷ 2
```

#### 2. معدل أساس المفاضلة
```
معدل أساس المفاضلة = 70% من المعدل النسبي + 30% من الامتحان التنافسي
```

### 🎖️ للدكتوراه

#### معدل المفاضلة
```
معدل المفاضلة للدكتوراه = معدل الماجستير × 0.6 + درجة الامتحان التنافسي × 0.4
```

## البيانات المطلوبة

### 🎓 للماجستير
1. **معدل الطالب المتقدم**: المعدل التراكمي للطالب الذي يريد التقديم
2. **معدل الطالب الأول**: المعدل التراكمي للطالب الأول في نفس القسم/الكلية
3. **درجة الامتحان التنافسي**: الدرجة التي حصل عليها الطالب في الامتحان التنافسي

### 🎖️ للدكتوراه
1. **معدل شهادة الماجستير**: المعدل التراكمي لشهادة الماجستير
2. **درجة الامتحان التنافسي**: الدرجة التي حصل عليها الطالب في الامتحان التنافسي

## كيفية الاستخدام

### من الصفحة الرئيسية:
1. اضغط على زر "حساب المعدل النسبي والتفاضلي"
2. **اختر نوع الدراسة**:
   - اضغط على "الماجستير" لحساب المعدل النسبي ومعدل أساس المفاضلة
   - اضغط على "الدكتوراه" لحساب معدل المفاضلة
3. أدخل البيانات المطلوبة حسب نوع الدراسة
4. اضغط على زر "احسب المعدل"

### النتائج:
ستظهر النتائج في **رسالة منبثقة وسط الصفحة** تحتوي على:
- **المعدل النسبي**: النتيجة المحسوبة وفقاً للمعادلة الأولى
- **معدل أساس المفاضلة**: النتيجة النهائية التي تُستخدم في المفاضلة
- **أزرار العمليات**:
  - زر "حساب جديد" لإعادة تعيين الحقول وإجراء حساب جديد
  - زر "إغلاق" لإغلاق الرسالة المنبثقة

## مثال عملي

### البيانات:
- معدل الطالب المتقدم: 85
- معدل الطالب الأول: 95
- درجة الامتحان التنافسي: 80

### الحسابات:
1. **المعدل النسبي**:
   ```
   = (85 × 100 ÷ 95 + 85 ÷ 2) ÷ 2
   = (89.47 + 42.5) ÷ 2
   = 131.97 ÷ 2
   = 65.99
   ```

2. **معدل أساس المفاضلة**:
   ```
   = 65.99 × 0.7 + 80 × 0.3
   = 46.19 + 24
   = 70.19
   ```

## الميزات التقنية

### التحقق من صحة البيانات:
- التأكد من إدخال قيم رقمية صحيحة
- التأكد من أن القيم أكبر من صفر
- التأكد من أن القيم لا تتجاوز 100

### واجهة المستخدم:
- تصميم متجاوب وجذاب
- رسوم متحركة سلسة
- دعم اللغة العربية
- عرض واضح للمعادلات المستخدمة
- **رسالة منبثقة وسط الصفحة** لعرض النتائج
- إمكانية إعادة تعيين الحقول مباشرة من الرسالة المنبثقة

### الاختبارات:
تم إنشاء مجموعة شاملة من الاختبارات للتأكد من:
- صحة حساب المعدل النسبي
- صحة حساب معدل أساس المفاضلة
- التعامل مع الحالات الحدية
- دقة النتائج مع قيم مختلفة

## الملفات المضافة/المعدلة

### ملفات جديدة:
- `lib/screens/grade_calculator_screen.dart`: شاشة حساب المعدل
- `test/grade_calculator_test.dart`: اختبارات المعادلات الرياضية

### ملفات معدلة:
- `lib/screens/home_screen.dart`: إضافة زر الانتقال للحاسبة

## ملاحظات مهمة
- جميع الحسابات تتم محلياً على الجهاز
- لا يتم حفظ أو إرسال أي بيانات
- النتائج دقيقة حتى منزلتين عشريتين
- يمكن استخدام الحاسبة عدة مرات دون إعادة تشغيل التطبيق
